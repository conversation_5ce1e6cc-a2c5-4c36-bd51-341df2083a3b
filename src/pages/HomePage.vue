<script setup lang="ts">
import Calendar from 'src/components/common/CalendarCom.vue';
import { useAuthStore } from '../stores/auth';
import type { User } from '../types/models';
import { ref, onMounted } from 'vue';

const authStore = useAuthStore();
const user = ref<User | undefined>(authStore.getCurrentUser());

onMounted(() => {
  user.value = authStore.getCurrentUser();
});
</script>

<template>
  <q-page padding>
    <div class="q-mb-lg">
      <h1 class="text-h4 q-mb-md">สวัสดี, {{ user?.name }}!</h1>
      <p class="text-subtitle1 q-mb-md">กรุณาเลือกระบบเพื่อเริ่มใช้งาน</p>
      <Calendar />
    </div>
    <div class="row q-col-gutter-md justify-start"></div>
  </q-page>
</template>
