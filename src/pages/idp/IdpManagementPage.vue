<template>
  <q-page padding class="q-gutter-y-lg">
    <div class="row justify-between items-center q-mb-md">
      <div class="text-h5">จัดการแผนพัฒนาบุคลากร</div>
      <div class="row q-gutter-x-sm">
        <q-btn label="เพิ่มแผน" unelevated icon="add" color="accent" @click="onClickAdd"> </q-btn>
      </div>
    </div>
    <q-table
      :rows="rows"
      :columns="competencyManagementColumns"
      row-key="id"
      flat
      bordered
      wrap-cells
      separator="cell"
    >
      <template v-slot:body-cell-actions="{ row }">
        <q-td class="text-center">
          <div class="q-gutter-x-sm flex justify-center">
            <q-btn dense unelevated class="view-icon" icon="edit" @click="onClickEdit(row)" />
            <q-btn dense unelevated class="view-icon" icon="delete" @click="onClickDelete(row)" />
          </div>
        </q-td>
      </template>
    </q-table>
  </q-page>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import { competencyManagementColumns } from 'src/data/table_columns';
import type { Competency } from 'src/types/models';
import { defineAsyncComponent, ref } from 'vue';

const $q = useQuasar();

const onClickEdit = (row: Competency) => {
  $q.dialog({
    component: defineAsyncComponent(() => import('src/components/idp/IdpForm.vue')),
    componentProps: {
      title: 'แก้ไขแผน',
      formData: row,
    },
    persistent: true,
  })
    .onOk((data: Competency) => {
      // Logic to handle updating the competency
      console.log('Updated competency data:', data);
      // Find and update the row in the table
      const index = rows.value.findIndex((item) => item.id === data.id);
      if (index !== -1) {
        rows.value[index] = { ...data };
      }
    })
    .onCancel(() => {
      console.log('Edit dialog cancelled');
    })
    .onDismiss(() => {
      console.log('Edit dialog dismissed');
    });
};

const onClickDelete = (row: Competency) => {
  // Logic to handle delete action
  console.log('Delete row:', row);
};

const onClickAdd = () => {
  $q.dialog({
    component: defineAsyncComponent(() => import('src/components/idp/IdpForm.vue')),
    componentProps: {
      title: 'สร้างแผนใหม่',
    },
    persistent: true,
  })
    .onOk((data: Competency) => {
      // Logic to handle saving the new competency
      console.log('New competency data:', data);
      // Add new competency to the table
      const newId = Math.max(...rows.value.map((r) => r.id)) + 1;
      rows.value.push({ ...data, id: newId });
    })
    .onCancel(() => {
      console.log('Add dialog cancelled');
    })
    .onDismiss(() => {
      console.log('Add dialog dismissed');
    });
};

// Mock data for competencies
const mockCompetencies: Competency[] = [
  {
    id: 1,
    name: 'การบริหารจัดการโครงการ',
    description: 'ความสามารถในการวางแผน ดำเนินการ และควบคุมโครงการให้สำเร็จตามเป้าหมาย',
    type: 'Core Competency',
    career: 'Management',
  },
  {
    id: 2,
    name: 'การสื่อสารและการนำเสนอ',
    description: 'ทักษะในการสื่อสาร การนำเสนอข้อมูล และการประสานงานกับผู้อื่น',
    type: 'Functional Competency',
    career: 'Communication',
  },
  {
    id: 3,
    name: 'การคิดเชิงวิเคราะห์',
    description: 'ความสามารถในการวิเคราะห์ปัญหา ประมวลผลข้อมูล และหาแนวทางแก้ไข',
    type: 'Technical Competency',
    career: 'Analysis',
  },
  {
    id: 4,
    name: 'ภาวะผู้นำ',
    description: 'ความสามารถในการเป็นผู้นำ การมอบหมายงาน และการสร้างแรงบันดาลใจให้ทีม',
    type: 'Leadership Competency',
    career: 'Management',
  },
  {
    id: 5,
    name: 'เทคโนโลยีสารสนเทศ',
    description: 'ทักษะการใช้เทคโนโลยี การใช้งานซอฟต์แวร์ และการปรับตัวกับเทคโนโลยีใหม่',
    type: 'Technical Competency',
    career: 'IT',
  },
];

const rows = ref<Competency[]>(mockCompetencies);
</script>

<style scoped></style>
