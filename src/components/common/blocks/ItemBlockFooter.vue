<script setup lang="ts">
import ToggleBtn from '../ToggleBtn.vue';

const props = defineProps<{
  label: string;
  isRequired?: boolean;
  type?: string;
}>();

const emit = defineEmits<{
  (e: 'duplicate'): void;
  (e: 'delete'): void;
  (e: 'update:isRequired', value: boolean): void;
  (e: 'click'): void;
}>();

// Handle toggle change
function handleToggleChange(value: boolean | undefined) {
  // Ensure we always emit a boolean value, defaulting to false if undefined
  const booleanValue = value ?? false;
  emit('update:isRequired', booleanValue);
}
</script>
<template>
  <div
    class="row items-center justify-end"
    style="max-height: 50px; width: 100%"
    @click="$emit('click')"
  >
    <q-btn
      v-if="props.type === 'RADIO'"
      flat
      round
      icon="open_in_new"
      padding="sm"
      class="bg-transparent"
      color="grey"
    />
    <q-btn
      flat
      round
      icon="content_copy"
      padding="sm"
      class="bg-transparent"
      color="grey"
      @click="$emit('duplicate')"
    />
    <q-btn
      flat
      round
      icon="delete"
      color="grey"
      padding="sm"
      class="bg-transparent"
      @click="$emit('delete')"
    />
    <q-separator vertical inset color="#898989" />
    <q-item-label style="padding: 10px">{{ label }}</q-item-label>
    <ToggleBtn
      :model-value="props.isRequired || false"
      @update:model-value="handleToggleChange"
      @click="$emit('click')"
    />
  </div>
</template>
<style scoped></style>
